// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
const { log } = require('console')
const COS = require('cos-nodejs-sdk-v5')
// jsdoc 语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
const cos = new COS({
  SecretId: 'AKIDkUOEuK8l9HBrNhu0wd5JLAmBTXNLOKD6',
  SecretKey: 'lHeprlTAh4MRVgnkuZ6ZuBKH4iL2hKxQ',
})
const params = {
  Bucket: 'obsidian-sync-1257140447', // 必须
  Region: 'ap-guangzhou',
}
const checkAuth = (httpInfo) => {
  const apiKey = httpInfo.headers['x-api-key'] || httpInfo.headers['X-API-KEY']
  const SECRET_KEY = 'zcjjj'

  if (!apiKey || apiKey !== SECRET_KEY) {
    return {
      code: 401,
      message: '未经授权的访问，请提供有效的 API 密钥',
    }
  }
  return null
}

module.exports = {
  _before: function () {},
  /**
   * 创建 cursor 笔记
   * @param {string} content
   * @param {string} fileName
   */
  async createMCPNote() {
    const httpInfo = this.getHttpInfo()

    let { content, fileName } = JSON.parse(httpInfo.body)
    try {
      const Key = fileName
      await cos.putObject({
        ...params,
        Key,
        Body: Buffer.from(content),
        ContentType: 'text/markdown; charset=utf-8',
      })
      return {
        code: 200,
        url: `https://${params.Bucket}.cos.${params.Region}.myqcloud.com/${Key}`,
      }
    } catch (err) {
      console.error(err)
      return {
        code: 400,
        message: '文件创建失败',
      }
    }
  },
  /**
   * 创建 MD 笔记
   * @param {string} content
   * @param {string} fileName
   */
  async createNote() {
    const httpInfo = this.getHttpInfo()
    const authResult = checkAuth(httpInfo)
    if (authResult) return authResult

    let { content, fileName, tableList, t } = JSON.parse(httpInfo.body)
    try {
      const Key = fileName
      await cos.putObject({
        ...params,
        Key,
        Body: Buffer.from(content),
        ContentType: 'text/markdown; charset=utf-8',
      })
      return {
        code: 200,
        url: `https://${params.Bucket}.cos.${params.Region}.myqcloud.com/${Key}`,
      }
    } catch (err) {
      console.error(err)
      return {
        code: 400,
        message: '文件创建失败',
      }
    }
  },
  /**
   * 获取菜单列表
   */
  async getMenus() {
    const httpInfo = this.getHttpInfo()
    const authResult = checkAuth(httpInfo)
    if (authResult) return authResult

    try {
      const data = await cos.getBucket({
        ...params,
      })

      return {
        code: 200,
        data: data.Contents,
      }
    } catch (err) {
      console.log(err)
      return {
        code: 400,
      }
    }
  },
  /**
   * 获取笔记详情
   */
  async getNote() {
    const httpInfo = this.getHttpInfo()
    const authResult = checkAuth(httpInfo)
    if (authResult) return authResult

    let { key } = JSON.parse(httpInfo.body)

    // 处理多 key 查询
    if (Array.isArray(key)) {
      try {
        const requests = key.map((k) =>
          cos
            .getObject({ ...params, Key: k })
            .then((data) => ({ key: k, data: data.Body.toString('utf8') }))
            .catch((err) => ({ key: k, error: err.message }))
        )

        const results = await Promise.allSettled(requests)

        const responseData = results.map((result) => {
          if (result.status === 'fulfilled') {
            return {
              key: result.value.key,
              data: result.value.data,
              status: 'success',
            }
          }
          return {
            key: result.reason.key,
            error: result.reason.error,
            status: 'failed',
          }
        })

        return {
          code: 200,
          data: responseData,
        }
      } catch (err) {
        console.error('批量查询失败：', err)
        return {
          code: 400,
          message: '批量查询失败',
        }
      }
    }

    // 保持原有单个查询逻辑
    console.log('入参', key)
    try {
      const data = await cos.getObject({
        ...params,
        Key: key,
      })
      return {
        code: 200,
        data: data.Body.toString('utf8'),
      }
    } catch (err) {
      console.log(err)
      return {
        code: 400,
        message: err.message,
      }
    }
  },
  /**
   * 删除笔记
   * @param {string} key 要删除的文件的键名
   */
  async deleteNote() {
    const httpInfo = this.getHttpInfo()
    const authResult = checkAuth(httpInfo)
    if (authResult) return authResult

    let { key } = JSON.parse(httpInfo.body)
    try {
      await cos.deleteObject({
        ...params,
        Key: key,
      })
      return {
        code: 200,
      }
    } catch (err) {
      console.log(err)
      return {
        code: 400,
        message: err.message,
      }
    }
  },

  /**
   * 批量更新普通话数据
   * 先清空 putonghua 表，然后使用批量覆盖的方式更新数据库中的数据
   * @param {Array} dataList 要更新的数据数组，每个元素包含完整的记录数据
   * @returns {Object} 包含操作结果的响应对象
   */
  async batchUpdatePutonghuaData() {
    const httpInfo = this.getHttpInfo()

    try {
      let { dataList } = JSON.parse(httpInfo.body)

      // 参数验证
      if (!dataList || !Array.isArray(dataList)) {
        return {
          code: 400,
          message: '参数错误：dataList 必须是数组',
        }
      }

      if (dataList.length === 0) {
        return {
          code: 400,
          message: '参数错误：dataList 不能为空',
        }
      }

      const db = uniCloud.database()
      const collection = db.collection('putonghua')

      let successCount = 0
      let errorCount = 0
      const errors = []

      console.log(`开始批量更新普通话数据，共 ${dataList.length} 条记录`)

      // 先清空表中的所有数据
      try {
        console.log('正在清空 putonghua 表...')
        const deleteResult = await collection.where({}).remove()
        console.log(`成功清空 putonghua 表，删除了 ${deleteResult.deleted} 条记录`)
      } catch (clearError) {
        console.error('清空 putonghua 表失败：', clearError)
        return {
          code: 500,
          message: '清空数据表失败：' + clearError.message,
        }
      }

      // 批量处理数据
      for (let i = 0; i < dataList.length; i++) {
        const item = dataList[i]
        try {
          // 添加时间戳
          const nowTime = new Date().toISOString()

          // 从数据中排除 _id 字段，因为 uniCloud 不允许更新 _id
          const { _id, ...dataWithoutId } = item

          const updateData = {
            ...dataWithoutId,
            updateTime: nowTime,
            // 如果没有 createTime，则添加
            createTime: item.createTime || nowTime,
          }

          // 使用 doc().set() 进行覆盖更新（如果不存在则创建）
          await collection.doc(_id).set(updateData)
          successCount++

          if ((i + 1) % 10 === 0) {
            console.log(`已处理 ${i + 1}/${dataList.length} 条记录`)
          }
        } catch (error) {
          errorCount++
          const errorMsg = `记录 ${item._id} (${item.character}) 更新失败：${error.message}`
          errors.push(errorMsg)
          console.error(errorMsg)
        }
      }

      console.log(`批量更新完成：成功 ${successCount} 条，失败 ${errorCount} 条`)

      // 返回操作结果
      const result = {
        code: errorCount === 0 ? 200 : 206, // 206 表示部分成功
        message: errorCount === 0 ? '批量更新成功' : '批量更新部分成功',
        data: {
          totalCount: dataList.length,
          successCount,
          errorCount,
          errors: errors.slice(0, 10), // 最多返回前 10 个错误信息
        },
      }

      return result
    } catch (err) {
      console.error('批量更新普通话数据失败：', err)
      return {
        code: 500,
        message: '服务器内部错误：' + err.message,
      }
    }
  },

  /**
   * 验证 lifeOS key
   * @param {string} key 用户输入的key
   * @returns {string} 验证结果："xpzgg" 表示成功，"xpzbg" 表示失败
   */
  async validateKey() {
    const httpInfo = this.getHttpInfo()

    try {
      let { key } = JSON.parse(httpInfo.body)

      // 参数验证
      if (!key || typeof key !== 'string') {
        return 'xpzbg'
      }

      const SECRET_KEY = 'zcjjj'

      // 验证key是否与SECRET_KEY一致
      if (key === SECRET_KEY) {
        return 'xpzgg'
      } else {
        return 'xpzbg'
      }
    } catch (err) {
      console.error('验证key失败：', err)
      return 'xpzbg'
    }
  },
}
