<script>
import initApp from '@/common/appInit.js'
import openApp from '@/common/openApp.js'
// #ifdef H5
openApp() //创建在 h5 端全局悬浮引导用户下载 app 的功能
// #endif
import checkIsAgree from '@/pages/uni-agree/utils/uni-agree.js'
import uniIdPageInit from '@/uni_modules/uni-id-pages/init.js'
import 'font-awesome/css/font-awesome.min.css'
export default {
  globalData: {
    syncStorage: {
      // _id: {
      //   data: params,
      //   tableName: table,
      //   isSyncing: false,
      // },
    },
    canSyncTime: 0,
    // 用于判断是否需要刷新页面 TODO: 是否有更优的做法？
    isRefreshPage: false,
    urlParams: {},
    searchText: '',
    appVersion: {},
    config: {},
    $i18n: {},
    $t: {},
    isPC: false,
    minWidth: 768, // PC 端最小宽度
    maxWidth: 9999, // PC 端最大宽度
    isConnected: true, // 是否有网络连接
    kzToken: 'pat_wmRv1MjfI4309rJx800rFVpTtRaqwbpPtyqD8LoTByUjPV9YTmrQTnSDM1uVNMXF',
    categoryList: [
      {
        _id: '1',
        name: '餐饮',
      },
      {
        _id: '2',
        name: '交通',
      },
      {
        _id: '3',
        name: '购物',
      },
      {
        _id: '4',
        name: '日用',
      },
      {
        _id: '5',
        name: '娱乐',
      },
      {
        _id: '6',
        name: '生活缴费',
      },
      {
        _id: '7',
        name: '住房',
      },
      {
        _id: '8',
        name: '医疗',
      },
      {
        _id: '9',
        name: '教育',
      },
      {
        _id: '10',
        name: '旅行',
      },
      {
        _id: '11',
        name: '人情',
      },
      {
        _id: '12',
        name: '其他',
      },
    ], // 分类列表
  },
  onLaunch: function () {
    console.log('App Launch')

    this.globalData.$i18n = this.$i18n
    this.globalData.$t = (str) => this.$t(str)
    initApp()
    uniIdPageInit()

    // 设置主题
    const savedTheme = uni.getStorageSync('theme') || 'ocean'
    this.setTheme(savedTheme)

    document.title = '0.0'

    // 检测lifeOS key
    this.checkLifeOSKey()

    // APP 端手动关闭启动页
    // #ifdef APP-PLUS
    plus.navigator.closeSplashscreen()
    // #endif

    uni.onPushMessage((res) => {
      if (res?.data?.payload?.channel !== 'UNI_CLOUD_SSE') {
        console.log('收到推送消息：', res) //监听推送消息
      }
    })

    // #ifdef APP-PLUS
    //checkIsAgree(); APP 端暂时先用原生默认生成的。目前，自定义方式启动 vue 界面时，原生层已经请求了部分权限这并不符合国家的法规
    // #endif

    // #ifdef H5
    // checkIsAgree(); // 默认不开启。目前全球，仅欧盟国家有网页端同意隐私权限的需要。如果需要可以自己去掉注视后生效
    // #endif

    // #ifdef APP-PLUS
    //idfa 有需要的用户在应用首次启动时自己获取存储到 storage 中
    /*var idfa = '';
			var manager = plus.ios.invoke('ASIdentifierManager', 'sharedManager');
			if(plus.ios.invoke(manager, 'isAdvertisingTrackingEnabled')){
				var identifier = plus.ios.invoke(manager, 'advertisingIdentifier');
				idfa = plus.ios.invoke(identifier, 'UUIDString');
				plus.ios.deleteObject(identifier);
			}
			plus.ios.deleteObject(manager);
			console.log('idfa = '+idfa);*/
    // #endif
  },
  onShow: function () {
    // console.log('App Show')
    // 在应用显示时重新应用主题，确保在应用重启后能正确应用保存的主题
    const savedTheme = uni.getStorageSync('theme') || 'ocean'
    this.setTheme(savedTheme)
  },
  onHide: function () {
    // console.log('App Hide')
  },
  methods: {
    setTheme(theme, isLoad = false) {
      const themeName = theme || 'ocean'
      uni.setStorageSync('theme', themeName)
      // #ifdef H5
      document.documentElement.setAttribute('data-theme', themeName)
      // #endif
      // #ifndef H5
      // 在非 H5 平台，需要应用主题到根元素
      setTimeout(
        () => {
          const pages = getCurrentPages()
          if (pages.length > 0) {
            const currentPage = pages[pages.length - 1]
            if (currentPage.$vm && currentPage.$vm.$el) {
              currentPage.$vm.$el.setAttribute('data-theme', themeName)
            }
          }
        },
        isLoad ? 0 : 500
      )
      // #endif
    },
    async checkLifeOSKey() {
      // 延迟检测，确保应用完全启动
      setTimeout(async () => {
        const lifeOSKey = uni.getStorageSync('lifeOSKey')
        if (!lifeOSKey) {
          // 如果没有lifeOS key，跳转到设置页面
          uni.reLaunch({
            url: '/pages/setting/lifeos-key'
          })
          return
        }

        // 如果有key，需要验证key是否正确
        try {
          const lifeOSObj = uniCloud.importObject('lifeOS')
          const validateResult = await lifeOSObj.validateKey({
            key: lifeOSKey
          })

          if (validateResult !== 'xpzgg') {
            // key验证失败，清除本地存储的key并跳转到设置页面
            uni.removeStorageSync('lifeOSKey')
            uni.reLaunch({
              url: '/pages/setting/lifeos-key'
            })
          }
        } catch (error) {
          console.error('验证lifeOS key时发生错误：', error)
          // 网络错误时，暂时不跳转，允许用户继续使用
          // 可以根据需要调整这个策略
        }
      }, 500)
    },
  },
}
</script>

<style>
@import './static/iconfont/iconfont.css';
page {
  background-color: #f8f9ff;
}
/*每个页面公共 css */
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
/* PC 端隐藏手机端 tab */
.uni-app--showleftwindow + .uni-tabbar-bottom {
  display: none;
}
.z-bottom {
  width: 100%;
  height: var(--window-bottom);
  /* height: var(--nav-bar-height); */
}
.zcj-table-sr .uni-table-tr {
  background: blue !important;
}
</style>
<style lang="scss">
@import './uni_modules/vk-uview-ui/index.scss';
@import './styles/variables.css';
@import './styles/fonts.css';

page {
  font-family: var(--font-sans);
}

// .uni-date-editor {
//   display: none;
// }
.z-collapse-title {
  display: flex;
  align-items: center;
  // justify-content: space-between;
  padding-left: 25rpx;
  height: 40px;
  background-color: #fff;
  color: #333;
  font-size: 25rpx;
  font-weight: 500;
}
.z-collapse-number {
  margin-left: auto;
  font-size: 24rpx;
  color: #999;
}

.status_bar {
  height: var(--status-bar-height);
  width: 100%;
}
* {
  box-sizing: border-box;
}
uni-input {
  height: inherit;
}
.status-bar-placeholder {
  height: var(--status-bar-height);
  width: 100%;
}
</style>
<style lang="scss">
.md-content {
  font-size: 14px;
  line-height: 1.6;
  color: var(--color-gray-800);
  word-break: break-word;
  white-space: normal;

  p {
    margin: 4px 0;
  }

  ul,
  ol {
    margin: 4px 0;
    padding-left: 18px;
  }

  li {
    margin: 2px 0;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 6px 0 4px;
    line-height: 1.3;
    font-weight: 600;
  }

  h1 {
    font-size: 16px;
  }

  h2 {
    font-size: 15px;
  }

  h3 {
    font-size: 14.5px;
  }

  h4,
  h5,
  h6 {
    font-size: 14px;
  }

  code {
    background: var(--color-gray-100);
    padding: 0 4px;
    border-radius: 3px;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  }

  pre {
    margin: 6px 0;
    padding: 8px 10px;
    background: var(--color-gray-100);
    border: 1px solid var(--color-gray-200);
    border-radius: 6px;
    overflow: auto;

    code {
      background: transparent;
      padding: 0;
    }
  }

  blockquote {
    margin: 6px 0;
    padding-left: 10px;
    border-left: 3px solid var(--color-gray-200);
    color: var(--color-gray-600);
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin: 6px 0;
    font-size: 13px;
  }

  th,
  td {
    border: 1px solid var(--color-gray-200);
    padding: 4px 6px;
    text-align: left;
  }

  hr {
    border: none;
    border-top: 1px solid var(--color-gray-200);
    margin: 6px 0;
  }

  a {
    color: var(--color-primary);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

// 标签高亮样式
.highlight-tag {
  color: #2196f3 !important;
  font-weight: 600 !important;
}
</style>
