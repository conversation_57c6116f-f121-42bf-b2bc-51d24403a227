<template>
  <view :class="!sys.isPC ? 'fixed-placeholder' : ''" class="page-margin">
    <view :class="{ fixed: !sys.isPC }" class="w-full top-0 z-100">
      <view class="status_bar"></view>
      <view class="flex flex-1 h-80 justify-between items-center">
        <view class="text-45">设置</view>
      </view>
    </view>
    <view class="setting-group">
      <view class="setting" @click="toUserInfo">
        <i class="fas fa-user setting-icon user-icon"></i>
        <view>{{ userInfo.nickname || userInfo.username || userInfo.mobile || '游客' }}</view>
        <view class="setting-right">
          <view v-if="!store.hasLogin" class="setting-tip">未登录</view>
        </view>
      </view>
    </view>
    <view class="setting-title">账单</view>
    <view class="setting-group">
      <view class="setting" @click="toClass">
        <i class="fas fa-tags setting-icon category-icon"></i>
        <view>分类管理</view>
        <view class="setting-right">
          <i class="fas fa-chevron-right more-icon"></i>
        </view>
      </view>
    </view>
    <view class="setting-title">系统设置</view>
    <view class="setting-group">
      <view class="setting" @click="toMemoryManagement">
        <i class="fas fa-brain setting-icon memory-icon"></i>
        <view>记忆管理</view>
        <view class="setting-right">
          <i class="fas fa-chevron-right more-icon"></i>
        </view>
      </view>
      <view class="setting" @click="toLifeOSKey">
        <i class="fas fa-key setting-icon key-icon"></i>
        <view>lifeOS key</view>
        <view class="setting-right">
          <i class="fas fa-chevron-right more-icon"></i>
        </view>
      </view>
      <view class="setting" @click="showSyncModal = true">
        <i class="fas fa-cloud-upload-alt setting-icon cloud-icon"></i>
        <view>云同步</view>
        <view class="setting-right">
          <view class="setting-tip">{{ isCloudSync ? '已开启' : '未开启，数据仅存本地' }}</view>
          <i class="fas fa-chevron-right more-icon"></i>
        </view>
      </view>
      <view class="setting" @click="showThemeModal = true">
        <i class="fas fa-palette setting-icon theme-icon"></i>
        <view>主题切换</view>
        <view class="setting-right">
          <view class="setting-tip">{{ getCurrentThemeName }}</view>
          <i class="fas fa-chevron-right more-icon"></i>
        </view>
      </view>
      <view class="setting" @click="toDB">
        <i class="fas fa-database setting-icon database-icon"></i>
        <view>数据库</view>
        <view class="setting-right" @click.stop="showResetModal = true">
          <view class="setting-tip" style="color: red">重置</view>
        </view>
      </view>
      <!-- #ifdef APP-PLUS -->
      <view class="setting" @click="checkVersion">
        <i class="fas fa-sync setting-icon update-icon"></i>
        <view>检查更新</view>
        <view class="setting-right">
          <view class="setting-tip">{{ appVersion.version }} </view>
          <view class="wh10 color-red" />
        </view>
      </view>
      <!-- #endif -->
    </view>
    <u-modal v-model="showSyncModal" title="云同步" show-cancel-button mask-close-able
      :content="isCloudSync ? '确定关闭吗？' : '确定开启吗？'" :confirm-text="isCloudSync ? '关闭' : '开启'"
      @confirm="onCloudSync"></u-modal>
    <u-modal v-model="showResetModal" title="重置数据库" content="此操作不可恢复，确定重置吗？" show-cancel-button mask-close-able
      confirm-text="重置" @confirm="onResetDB"></u-modal>

    <u-modal v-model="showThemeModal" title="选择主题" :show-confirm-button="false" mask-close-able confirm-text="关闭">
      <view class="p-3">
        <view class="themes-grid">
          <view v-for="theme in themes" :key="theme.name" class="theme-option" @click="changeTheme(theme.name)">
            <view class="theme-preview" :style="{
              background: `linear-gradient(135deg, ${getThemeColorPreview(
                theme.name,
                'primary'
              )}, ${getThemeColorPreview(theme.name, 'accent')})`,
              border: currentTheme === theme.name ? '3px solid var(--color-primary)' : '1px solid #eee',
            }">
              <view v-if="currentTheme === theme.name" class="theme-selected-indicator">✓</view>
            </view>
            <view class="theme-name">{{ theme.label }}</view>
          </view>
        </view>
      </view>
    </u-modal>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import localDB from '@/api/database'
import checkUpdate from '@/uni_modules/uni-upgrade-center-app/utils/check-update'
import callCheckVersion from '@/uni_modules/uni-upgrade-center-app/utils/call-check-version'
import { store, mutations } from '@/uni_modules/uni-id-pages/common/store.js'
import useIsPC from '@/hooks/useIsPC'
import { router } from '@/utils/tools'

const sys = useIsPC()

// 云同步开关
const showSyncModal = ref(false)
const showThemeModal = ref(false)
const isCloudSync = ref(false)

// 从全局工具中获取云同步状态
if (typeof toolCloudSync !== 'undefined' && toolCloudSync.isOpen) {
  isCloudSync.value = toolCloudSync.isOpen()
}

const themes = ref([
  { name: 'ocean', label: '海洋', primary: '#3498db', accent: '#1abc9c' },
  { name: 'forest', label: '森林', primary: '#4caf50', accent: '#81c784' },
  { name: 'sunset', label: '日落', primary: '#e77e23', accent: '#d35400' },
  { name: 'berry', label: '莓果', primary: '#c35b80', accent: '#d78da9' },
  { name: 'mint', label: '薄荷', primary: '#009688', accent: '#00bfa5' },
  { name: 'purple', label: '紫晶', primary: '#5e6ad2', accent: '#9fa8da' },
  { name: 'slate', label: '石板', primary: '#607d8b', accent: '#546e7a' },
  { name: 'sunshine', label: '阳光', primary: '#D4AC6E', accent: '#E5C495' },
  { name: 'coffee', label: '咖啡', primary: '#795548', accent: '#8d6e63' },
  { name: 'ruby', label: '红宝石', primary: '#d76c6c', accent: '#e58e8e' },
])

// 获取当前主题
const currentTheme = ref(uni.getStorageSync('theme') || 'ocean')

const changeTheme = (themeName: string) => {
  currentTheme.value = themeName
  const app = getApp()
  if (app && app.setTheme) {
    app.setTheme(themeName)
  }
  showThemeModal.value = false
}

const onCloudSync = () => {
  isCloudSync.value = !isCloudSync.value
  if (typeof toolCloudSync !== 'undefined') {
    if (isCloudSync.value) {
      toolCloudSync.open()
      // 如果有全局同步方法则调用
      if (typeof syncToServer === 'function') {
        syncToServer()
      }
    } else {
      toolCloudSync.close()
    }
  }
}



// 重置数据库
const showResetModal = ref(false)
const onResetDB = async () => {
  uni.showLoading({
    title: '重置中...',
  })
  uni.removeStorageSync('lastSyncTime')
  await localDB.delete()
  setTimeout(() => {
    uni.hideLoading()
    // #ifdef APP-PLUS
    plus.runtime.restart()
    // #endif
    // #ifdef H5
    window.location.reload()
    // #endif
  }, 500)
}

// 用户信息
const userInfo = computed(() => store.userInfo)

// 检查更新
const checkVersion = async () => {
  uni.showLoading({
    title: '检查更新...',
  })
  let res = await callCheckVersion()
  uni.hideLoading()
  if (res.result.code > 0) {
    checkUpdate()
  } else {
    uni.showToast({
      title: res.result.message,
      icon: 'none',
    })
  }
}

const toUserInfo = () => {
  if (!store.hasLogin) {
    uni.showModal({
      title: '数据同步提示',
      content: '登录后将使用云端数据覆盖本地数据，未存储的本地数据将丢失，是否继续？',
      confirmText: '继续登录',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          // 用户确认继续，清空数据库并跳转
          uni.removeStorageSync('lastSyncTime')
          await localDB.delete()
          uni.navigateTo({
            url: '/pages/setting/account',
          })
        }
      },
    })
  } else {
    uni.navigateTo({
      url: '/pages/setting/account',
    })
  }
}

const toClass = () => {
  uni.navigateTo({
    url: '/pages/bill/category',
  })
}

const toMemoryManagement = () => {
  router.push('/pages/memory/memory-management')
}

const toLifeOSKey = () => {
  router.push('/pages/setting/lifeos-key')
}

const toDB = () => {
  uni.navigateTo({
    url: '/pages/setting/db-sync',
  })
}

// #ifdef APP-PLUS
const appVersion = computed(() => {
  return getApp().appVersion
})
// #endif

const getCurrentThemeName = computed(() => {
  const theme = themes.value.find((t) => t.name === currentTheme.value)
  return theme ? theme.label : '未知主题'
})

const getThemeColorPreview = (themeName: string, type: 'primary' | 'accent') => {
  const theme = themes.value.find((t) => t.name === themeName)
  if (theme) {
    if (type === 'primary') {
      return theme.primary
    } else if (type === 'accent') {
      return theme.accent
    }
  }
  return '#e0e0e0' // Default for unknown theme or type
}
</script>

<style scoped lang="scss">
.setting-title {
  margin: 60rpx 0 30rpx;
  font-size: 32rpx;
  color: #333;
}

.setting-group {
  margin: 30rpx 0rpx;
  $radius: 20rpx;
  border-radius: $radius;
  overflow: hidden;
  .setting {
    display: flex;
    align-items: center;
    background-color: #fff;
    $padding: 40rpx;
    padding: $padding;
    // &:not(:last-child) {
    //   border-bottom: 1rpx solid #f0f0f0;
    // }
    .setting-icon {
      margin-right: 20rpx;
      font-size: 22px;
      width: 24px;
      text-align: center;
    }

    .user-icon {
      color: #3498db; // 蓝色
    }

    .category-icon {
      color: #e67e22; // 橙色
    }

    .memory-icon {
      color: #e74c3c; // 红色
    }

    .key-icon {
      color: #f1c40f; // 黄色
    }

    .cloud-icon {
      color: #2ecc71; // 绿色
    }

    .database-icon {
      color: #9b59b6; // 紫色
    }

    .update-icon {
      color: #1abc9c; // 青色
    }

    .theme-icon {
      color: #34495e; // 深蓝灰色
    }

    .setting-right {
      margin-left: auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .setting-tip {
        color: #999;
        font-size: 25rpx;
      }
      .more-icon {
        margin-left: 5rpx;
        color: #999;
        font-size: 16px;
      }
    }
  }
}

.fixed-placeholder {
  padding-top: calc(var(--status-bar-height) + 80rpx);
}

.themes-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20rpx;
  padding: 20rpx;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.theme-preview {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-bottom: 10rpx;
  position: relative;
  transition: all 0.2s;
}

.theme-preview:active {
  transform: scale(0.95);
}

.theme-name {
  font-size: 20rpx;
  color: #666;
}

.theme-selected-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20rpx;
  font-weight: bold;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}
</style>
