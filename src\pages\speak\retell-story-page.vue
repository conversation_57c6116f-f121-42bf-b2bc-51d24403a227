<template>
  <view class="retell-story-page">
    <z-page-navbar title="复述故事">
      <template #right>
        <div class="action-btn" @click="showRecordListPopup = true" aria-label="训练记录">
          <i class="fas fa-archive"></i>
        </div>
      </template>
    </z-page-navbar>

    <view class="content-container">
      <!-- 故事原文 -->
      <view class="card story-card">
        <view class="card-header" @click="isStoryCollapsed = !isStoryCollapsed">
          <i class="card-icon fas fa-book-open"></i>
          <view class="card-title">故事原文</view>
          <view class="reset-btn" @click.stop="handleResetStory" v-if="story.text">
            <i class="fas fa-sync-alt"></i>
          </view>
        </view>
        <view v-show="!isStoryCollapsed" class="story-content">
          <view v-if="story.text" class="story-text">{{ story.text }}</view>
          <view v-else class="story-placeholder">
            <view v-if="!isGenerating" class="button-group">
              <view class="generate-btn" @click.stop="generateStoryMaterial">
                <text>生成复述素材</text>
              </view>
              <view class="select-note-btn" @click.stop="selectFromNotes">
                <text>选择笔记</text>
              </view>
            </view>
            <z-loading v-else text="复述素材生成中" :font-size="32" padding="20px 0" />
          </view>
        </view>
        <view v-if="story.keywords.length > 0" class="keywords-section">
          <view v-for="keyword in story.keywords" :key="keyword" class="keyword-tag">
            {{ keyword }}
          </view>
        </view>
      </view>

      <!-- 用户复述 -->
      <view class="card retelling-card">
        <view class="card-header" @click="isRetellingCollapsed = !isRetellingCollapsed">
          <i class="card-icon fas fa-microphone"></i>
          <view class="card-title">你的复述
            <text v-if="currentEvaluation" class="attempt-label">(第 {{ currentEvaluation.attempt }} 次)</text>
          </view>
          <view v-if="evaluationHistory.length > 1 && !isRecording" class="history-btn"
            @click.stop="showHistoryPopup = true">
            <i class="fas fa-history"></i>
            <text style="margin-left: 4px">复述历史</text>
          </view>
        </view>
        <view v-show="!isRetellingCollapsed" class="retelling-content">
          <!-- 音频播放器 - 使用 z-audio-player 组件替换原来的自定义播放器 -->
          <z-audio-player v-if="currentAudioURL" :src="currentAudioURL" :showTime="true"
            :themeColor="'var(--color-primary, #007aff)'" class="retell-audio-player" ref="audioPlayerRef"
            @play="onAudioPlay" @pause="onAudioPause" @ended="onAudioEnded" :enableTranscription="true"
            :initial-sentences="currentEvaluation && currentEvaluation.sentences"
            @transcription-start="handleTranscriptionStart" @transcription-end="handleTranscriptionEnd" />
          <!-- 复述文本 -->
          <view v-else class="retelling-text">{{
            userRetelling || '请使用下方的语音或文本输入框进行复述...'
          }}</view>
        </view>
      </view>

      <!-- AI 反馈 -->
      <view class="card feedback-card" v-if="(currentEvaluation || isEvaluating) && !isRecording">
        <view class="card-header" @click="isFeedbackCollapsed = !isFeedbackCollapsed">
          <i class="card-icon fas fa-comment-dots"></i>
          <view class="card-title">AI 反馈</view>
        </view>
        <view v-show="!isFeedbackCollapsed" class="feedback-content">
          <z-loading v-if="isEvaluating" text="正在评价中" :font-size="32" padding="40rpx 0" />
          <template v-else-if="currentEvaluation">
            <view class="feedback-summary">{{ currentEvaluation.summaryText }}</view>
            <view class="feedback-details">
              <view v-for="item in currentEvaluation.details" :key="item.metric" class="detail-item">
                <div class="detail-item-header">
                  <i :class="['metric-icon', item.icon]"></i>
                  <h4 class="metric-name">{{ item.metric }}</h4>
                </div>
                <p class="metric-feedback">{{ item.feedback }}</p>
              </view>
            </view>
          </template>
        </view>
      </view>

      <!-- 输入控制区域 -->
      <view class="input-container">
        <z-message-input v-model="inputMessage" placeholder="输入你的复述或点击麦克风图标录音..." @send="handleTextSubmit"
          @send-audio="handleAudioSubmit" @upload-progress="handleUploadProgress" @error="handleRecordError"
          :max-duration="120000" audio-format="mp3" cloud-path="speak/" />
      </view>
    </view>

    <l-history-popup v-model:show="showHistoryPopup" title="复述历史" :history-list="evaluationHistory"
      :current-item-id="currentEvaluation?.id" @select-item="handleSelectHistoryItem" />

    <!-- 训练记录弹窗 -->
    <uni-popup ref="recordListPopupRef" type="bottom" @change="handleRecordListPopupChange" style="z-index: 10000">
      <view class="record-popup-content">
        <view class="record-popup-header">
          <text class="record-popup-title">训练记录</text>
          <view class="header-actions">
            <text class="clear-all-btn" @click="handleClearAllRecords">全部清空</text>
            <view class="record-popup-close-icon" @click="closeRecordListPopup">
              <uni-icons type="closeempty" size="20"></uni-icons>
            </view>
          </view>
        </view>
        <scroll-view scroll-y class="record-popup-scroll-view">
          <z-loading v-if="isLoadingRecords" text="正在加载..." :font-size="32" padding="40rpx 0" />
          <view v-else-if="!parsedRecords.length" class="record-popup-empty-state">
            <text>暂无记录</text>
          </view>
          <view v-else class="record-popup-list">
            <view v-for="record in parsedRecords" :key="record._id"
              :class="['record-popup-item', { active: record._id === currentRecordId }]"
              @click="handleSelectRecordAndClosePopup(record)">
              <view class="record-popup-item-main">
                <view class="record-popup-item-header">
                  <text class="record-popup-date">{{ formatDateForRecord(record.createTime) }}</text>
                </view>
                <view class="record-popup-keywords">
                  <uni-tag v-for="(keyword, index) in record.keywords" :key="index" :text="keyword" type="primary"
                    :inverted="true" size="mini" class="record-popup-keyword-tag" />
                </view>
                <text class="record-popup-summary">{{ record.summaryText }}</text>
              </view>
              <view class="record-popup-item-delete" @click.stop="handleDeleteRecord(record._id)">
                <i class="fas fa-trash-alt"></i>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>

    <!-- Debug Modal -->
    <z-debug-modal v-model:show="debugModalVisible" :title="debugModalData.title" :error-type="debugModalData.errorType"
      :error-message="debugModalData.errorMessage" :error-details="debugModalData.errorDetails"
      @close="debugModalVisible = false" />
  </view>
</template>

<script setup>
import { ref, computed, watch, onUnmounted, nextTick, onMounted } from 'vue'
import ZMessageInput from '@/components/z-message-input/z-message-input.vue'
import ZDebugModal from '@/components/z-debug-modal.vue'
import ZAudioPlayer from '@/components/z-audio-player/z-audio-player.vue'
import { router } from '@/utils/tools'
// import { generateRetellConetnt, evaluateRetell } from '@/api/speak' // 已迁移到云函数
import { addChatRecordApi, getChatRecordListApi, updateChatRecordApi, delChatRecordApi } from '@/api/chatRecord'
import ZLoading from '@/components/z-loading/index.vue'

// 初始化云函数对象
const speakObj = uniCloud.importObject('speak')

// XML 标签解析函数
function parseXmlTags(xmlString) {
  const result = {}

  // HTML 反转义函数
  function unescapeHtml(text) {
    return text
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
  }

  // 提取标签内容的通用函数
  function extractTag(tagName) {
    const regex = new RegExp(`<${tagName}>(.*?)</${tagName}>`, 's')
    const match = xmlString.match(regex)
    return match ? unescapeHtml(match[1]) : ''
  }

  // 解析各种标签
  result.title = extractTag('title')
  result.content = extractTag('content')
  result.keyWord = extractTag('keyword')

  // 解析评价相关标签
  result['内容完整性'] = extractTag('content_completeness')
  result['表达流畅度'] = extractTag('expression_fluency')
  result['语法和词汇'] = extractTag('grammar_vocabulary')
  result['理解深度'] = extractTag('understanding_depth')
  result['总体评价'] = extractTag('overall_evaluation')

  // 解析关键词故事评价标签
  result['关键词使用'] = extractTag('keyword_usage')
  result['故事创意'] = extractTag('story_creativity')
  result['AI 示例故事'] = extractTag('ai_example_story')
  result['故事改良版'] = extractTag('improved_story')

  return result
}

const recordListPopupRef = ref(null)

// Debug Modal 状态
const debugModalVisible = ref(false)
const debugModalData = ref({
  title: 'Debug 信息',
  errorType: '',
  errorMessage: '',
  errorDetails: null,
})

// 显示 Debug 弹窗
const showDebugModal = (type, message, details) => {
  debugModalData.value = {
    title: '录音错误详情',
    errorType: type || '未知错误类型',
    errorMessage: message || '发生错误',
    errorDetails: details || {},
  }
  debugModalVisible.value = true
}

const story = ref({
  text: '',
  keywords: [],
})

// 生成复述素材的函数
const generateStoryMaterial = async (e) => {
  // 阻止事件冒泡，避免触发卡片收起功能
  e && e.stopPropagation()

  // 设置加载状态为 true
  isGenerating.value = true

  try {
    // 调用云函数接口
    const xmlString = await speakObj.generateRetellConetnt('请生成一个关于友谊的有趣故事')
    console.log('云函数返回的 XML 字符串:', xmlString)

    // 解析 XML 标签格式为对象
    const result = parseXmlTags(xmlString)
    console.log('解析后的结果:', result)

    // A new story marks a new session, reset everything first
    handleResetStory(false)

    // 更新故事内容
    story.value = {
      text: result.content,
      keywords: result.keyWord.split('，'), // 拆分关键词
    }
  } catch (error) {
    console.error('生成复述素材失败：', error)
    uni.showToast({
      title: '生成失败，请重试',
      icon: 'none',
    })
  } finally {
    // 无论成功还是失败，都设置加载状态为 false
    isGenerating.value = false
  }
}

// 选择笔记作为复述材料
const selectFromNotes = () => {
  router.push('/pages/speak/speak-menu')
}

// 检查并加载缓存的笔记内容
const checkCachedNoteContent = () => {
  try {
    const cachedContent = uni.getStorageSync('speakNoteContent')
    if (cachedContent) {
      // A new story marks a new session, reset everything first
      handleResetStory(false)

      // 设置故事内容
      story.value = {
        text: cachedContent,
        keywords: [], // 笔记内容没有关键词
      }

      // 清除缓存，避免重复使用
      uni.removeStorageSync('speakNoteContent')

      uni.showToast({
        title: '已加载笔记内容',
        icon: 'success',
      })
    }
  } catch (error) {
    console.error('读取缓存笔记内容失败：', error)
  }
}

// 页面初始化
onMounted(() => {
  checkCachedNoteContent()
})

const isStoryCollapsed = ref(false)
const isRetellingCollapsed = ref(false)
const isFeedbackCollapsed = ref(false)
const userRetelling = ref('')
const inputMessage = ref('')
const isRecording = ref(false)
const isEvaluating = ref(false) // 控制评价加载状态
const evaluationHistory = ref([])
const currentEvaluationId = ref(null)
const isGenerating = ref(false) // 控制生成素材的加载状态
const isTranscribing = ref(false) // 控制转写状态

const showHistoryPopup = ref(false)
const showRecordListPopup = ref(false)
const records = ref([])
const isLoadingRecords = ref(false)
const currentRecordId = ref(null)

const fetchRecords = async () => {
  isLoadingRecords.value = true
  try {
    const allRecords = await getChatRecordListApi()
    records.value = allRecords
      .filter((r) => {
        try {
          if (typeof r.content === 'string' && r.content.trim().startsWith('{')) {
            const content = JSON.parse(r.content)
            return content.type === 'retellStory'
          }
          return false
        } catch {
          return false
        }
      })
      .sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
  } catch (error) {
    console.error('Failed to fetch records', error)
    uni.showToast({ title: '加载记录失败', icon: 'none' })
    records.value = []
  } finally {
    isLoadingRecords.value = false
  }
}

watch(showRecordListPopup, (newValue) => {
  if (newValue) {
    fetchRecords()
    recordListPopupRef.value?.open()
  } else {
    recordListPopupRef.value?.close()
  }
})

const handleSelectRecordAndClosePopup = (record) => {
  handleSelectRecord(record)
  closeRecordListPopup()
}

const handleRecordListPopupChange = (e) => {
  if (!e.show) {
    showRecordListPopup.value = false
  }
}

const closeRecordListPopup = () => {
  showRecordListPopup.value = false
}

const parsedRecords = computed(() => {
  if (!records.value || records.value.length === 0) {
    return []
  }
  return records.value
    .map((record) => {
      try {
        const content = JSON.parse(record.content)
        const history = content.history || []
        return {
          _id: record._id,
          createTime: record.createTime,
          keywords: content.story?.keywords || [],
          history: history,
          story: content.story,
          summaryText: history.length > 0 ? history[history.length - 1].summaryText : '暂无评价',
        }
      } catch (e) {
        console.error('Failed to parse record content:', e)
        return null
      }
    })
    .filter(Boolean)
})

// 音频播放器相关状态
const currentAudioURL = ref('') // 当前音频 URL
const isPlaying = ref(false) // 是否正在播放
const audioPlayerRef = ref(null) // 音频播放器组件引用

// 音频播放事件处理
const onAudioPlay = () => {
  isPlaying.value = true
}

const onAudioPause = () => {
  isPlaying.value = false
}

const onAudioEnded = () => {
  isPlaying.value = false
}

// 当前选中的评价记录
const currentEvaluation = computed(() => {
  if (!currentEvaluationId.value && evaluationHistory.value.length > 0) {
    // 默认展示最新的评价
    return evaluationHistory.value[evaluationHistory.value.length - 1]
  }
  return evaluationHistory.value.find((item) => item.id === currentEvaluationId.value) || null
})

// 监听当前评价变化，更新显示的复述内容和音频
watch(currentEvaluation, (newVal) => {
  if (newVal) {
    userRetelling.value = newVal.retellingText

    // 如果有新的评价记录，更新音频 URL
    if (newVal.audioURL) {
      currentAudioURL.value = newVal.audioURL
    } else {
      // 没有音频的情况
      currentAudioURL.value = ''
    }
  }
})

const handleSelectRecord = (selectedRecord) => {
  console.log('Restoring from record:', selectedRecord)

  const restoredHistory = selectedRecord.history || []

  evaluationHistory.value = restoredHistory.map((item, index) => ({
    ...item,
    attempt: index + 1,
    timestamp: new Date(item.timestamp || selectedRecord.createTime),
  }))

  if (evaluationHistory.value.length > 0) {
    currentEvaluationId.value = evaluationHistory.value[evaluationHistory.value.length - 1].id
  } else {
    currentEvaluationId.value = null
  }

  // Restore story and record ID
  story.value = selectedRecord.story || { text: '', keywords: [] }
  currentRecordId.value = selectedRecord._id

  uni.showToast({
    title: '记录已恢复',
    icon: 'none',
  })
}

// 切换到指定 ID 的评价记录
const switchToEvaluation = (id) => {
  // 停止当前音频播放
  if (audioPlayerRef.value && isPlaying.value) {
    audioPlayerRef.value.pause()
  }

  currentEvaluationId.value = id
  uni.pageScrollTo({
    scrollTop: 0,
    duration: 300,
  })
}

const handleSelectHistoryItem = (id) => {
  currentEvaluationId.value = id
}

// 格式化时间戳显示
const formatTimestamp = (date) => {
  if (!date) return ''
  const d = new Date(date)
  return `${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}`
}

const formatDateForRecord = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
    date.getDate()
  ).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 创建评价记录
const createEvaluation = async (attemptNumber, submittedText, sentences = null, audioData = null) => {
  // 使用加载状态
  const isLoading = ref(true)

  try {
    // 构建评价请求文本，包含原故事和用户复述内容
    const evaluationText = `原故事：${story.value.text || '未提供原文'}\n\n用户复述：${submittedText}`

    // 调用云函数接口
    const xmlString = await speakObj.evaluateRetell(evaluationText)
    console.log('评价云函数返回的 XML 字符串:', xmlString)

    // 解析 XML 标签格式为对象
    const evaluationResult = parseXmlTags(xmlString)
    console.log('解析后的评价结果:', evaluationResult)
    uni.vibrateShort()
    // 使用模拟数据代替真实 API 调用
    // const evaluationResult = {
    //   总体评价：'复述内容基本完整，表达较为流畅',
    //   内容完整性：'复述包含了原文的主要内容，但遗漏了一些细节',
    //   表达流畅度：'语句连贯，表达自然',
    //   语法和词汇：'词汇使用恰当，有少量语法错误',
    //   理解深度：'对故事主题有基本理解，可以更深入分析故事寓意',
    // }

    // 映射 API 返回的评价结果到 UI 展示所需格式
    return {
      id: `attempt-${Date.now()}-${attemptNumber}`,
      attempt: attemptNumber,
      retellingText: submittedText,
      sentences: sentences, // 保存详细文案
      timestamp: new Date(),
      summaryText: evaluationResult['总体评价'] || '分析完成',
      details: [
        { metric: '内容完整性', icon: 'fas fa-clipboard-check', feedback: evaluationResult['内容完整性'] },
        { metric: '表达流畅度', icon: 'fas fa-wind', feedback: evaluationResult['表达流畅度'] },
        { metric: '语法和词汇', icon: 'fas fa-spell-check', feedback: evaluationResult['语法和词汇'] },
        { metric: '理解深度', icon: 'fas fa-brain', feedback: evaluationResult['理解深度'] },
      ],
      audioURL: audioData ? audioData.tempFileURL || '' : '',
      audioDuration: audioData ? audioData.duration : 0,
      fileID: audioData ? audioData.fileID : '',
    }
  } catch (error) {
    console.error('评价失败：', error)
    // 显示错误信息
    showDebugModal('评价错误', '获取评价失败，请重试', error)

    uni.showToast({
      title: '评价失败，请重试',
      icon: 'none',
    })

    // 返回一个基本的评价记录，表示出错
    return {
      id: `attempt-${Date.now()}-${attemptNumber}`,
      attempt: attemptNumber,
      retellingText: submittedText,
      sentences: sentences, // 即使失败也保存
      timestamp: new Date(),
      summaryText: '评价请求失败，请重试',
      details: [
        { metric: '内容完整性', icon: 'fas fa-clipboard-check', feedback: '无法获取评价' },
        { metric: '表达流畅度', icon: 'fas fa-wind', feedback: '无法获取评价' },
        { metric: '语法和词汇', icon: 'fas fa-spell-check', feedback: '无法获取评价' },
        { metric: '理解深度', icon: 'fas fa-brain', feedback: '无法获取评价' },
      ],
      audioURL: audioData ? audioData.tempFileURL || '' : '',
      audioDuration: audioData ? audioData.duration : 0,
      fileID: audioData ? audioData.fileID : '',
    }
  }
}

const saveRecord = async () => {
  if (evaluationHistory.value.length === 0) return

  const persistentHistory = evaluationHistory.value.map((e) => {
    const persistentItem = { ...e }
    delete persistentItem.attempt
    persistentItem.timestamp = new Date(persistentItem.timestamp).toISOString()
    return persistentItem
  })

  try {
    const recordPayload = {
      title: story.value.text.substring(0, 20), // Use first 20 chars of story as title
      content: JSON.stringify({
        type: 'retellStory',
        story: story.value,
        history: persistentHistory,
      }),
    }

    if (currentRecordId.value) {
      await updateChatRecordApi(currentRecordId.value, recordPayload)
      console.log('训练记录已更新：', currentRecordId.value)
    } else {
      const newId = await addChatRecordApi(recordPayload)
      currentRecordId.value = newId
      console.log('新训练记录已保存：', newId)
    }
  } catch (error) {
    console.error('保存训练记录失败：', error)
  }
}

// 处理文本提交
const handleTextSubmit = async () => {
  if (!inputMessage.value.trim()) return

  // 更新用户复述内容
  userRetelling.value = inputMessage.value

  // 显示加载状态
  isEvaluating.value = true

  try {
    // 创建新的评价记录 - 纯文本没有 sentences
    const attemptNumber = evaluationHistory.value.length + 1
    const newEvaluation = await createEvaluation(attemptNumber, inputMessage.value, null)
    if (newEvaluation) {
      evaluationHistory.value.push(newEvaluation)
      currentEvaluationId.value = newEvaluation.id
      inputMessage.value = ''
      saveRecord()
    }
  } catch (error) {
    console.error('文本评价失败：', error)
  } finally {
    // 隐藏加载状态
    isEvaluating.value = false
    uni.vibrateShort()
  }
}

// 处理音频提交
const handleAudioSubmit = async (audioData) => {
  console.log('收到音频', audioData)
  isRecording.value = true

  // 存储当前音频数据，用于转写完成后创建评价
  currentAudioData.value = audioData

  // 更新音频 URL
  if (audioData && audioData.tempFileURL) {
    currentAudioURL.value = audioData.tempFileURL
  }

  // 使用 nextTick 确保 DOM 更新后，player 组件的 ref 可用
  nextTick(() => {
    if (audioPlayerRef.value) {
      // 显式调用子组件的转写方法
      audioPlayerRef.value.transcribe()
    }
  })

  // 注意：音频转写现在由 z-audio-player 组件处理
  // 通过 transcription-end 事件来接收转写结果
}

// 处理转写开始
const handleTranscriptionStart = () => {
  console.log('开始转写')
  isTranscribing.value = true
}

// 处理转写结束
const handleTranscriptionEnd = async (result) => {
  console.log('转写结束', result)
  isTranscribing.value = false
  isRecording.value = false

  if (result.success && result.transcript) {
    // 更新用户复述内容
    userRetelling.value = result.transcript

    // 显示加载状态
    isEvaluating.value = true

    try {
      // 创建新的评价记录
      const attemptNumber = evaluationHistory.value.length + 1
      const newEvaluation = await createEvaluation(attemptNumber, result.transcript, result.sentences, {
        tempFileURL: currentAudioData.value.tempFileURL,
        duration: currentAudioData.value.duration,
        fileID: currentAudioData.value.fileID,
      })
      if (newEvaluation) {
        evaluationHistory.value.push(newEvaluation)

        // 切换到新创建的评价
        currentEvaluationId.value = newEvaluation.id

        // 清空临时数据
        currentAudioData.value = null
        saveRecord()
      }
    } catch (error) {
      console.error('转写评价失败：', error)
    } finally {
      // 隐藏加载状态
      isEvaluating.value = false
      uni.vibrateShort()
    }
  } else if (result.error) {
    // 显示错误详情
    showDebugModal('转写失败', '语音转写失败，请重试', result.error)

    uni.showToast({
      title: '转写失败，请重试',
      icon: 'none',
    })
  }
}

// 处理上传进度
const handleUploadProgress = (progress) => {
  console.log('上传进度', progress)
}

// 处理录音错误
const handleRecordError = (error) => {
  console.error('录音错误', error)
  isRecording.value = false
  isTranscribing.value = false

  let errorMessage = '录音出错，请重试'
  let errorType = '未知错误'

  // 提取更有用的错误信息
  if (error && error.type === 'upload') {
    errorType = '上传错误'
    if (error.error && error.error.errMsg) {
      errorMessage = `上传失败：${error.error.errMsg}`
    } else {
      errorMessage = '文件上传失败，请重试'
    }
  } else if (error && error.type === 'transcription') {
    errorType = '转写错误'
    errorMessage = '语音转写失败，请重试'
  } else if (error && error.type === 'send') {
    errorType = '发送错误'
    errorMessage = '发送录音失败，请重试'
  }

  // 显示 Debug 弹窗
  showDebugModal(errorType, errorMessage, error)

  uni.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 3000,
  })
}

// 存储当前音频数据，用于转写完成后创建评价
const currentAudioData = ref(null)

const handleDeleteRecord = async (recordId) => {
  try {
    await delChatRecordApi(recordId)
    uni.showToast({ title: '删除成功', icon: 'none' })
    records.value = records.value.filter((r) => r._id !== recordId)
    if (recordId === currentRecordId.value) {
      handleResetStory(false)
    }
  } catch (error) {
    console.error('Failed to delete record:', error)
    uni.showToast({ title: '删除失败', icon: 'none' })
  }
}

const handleClearAllRecords = async () => {
  if (records.value.length === 0) {
    uni.showToast({ title: '没有可清空的记录', icon: 'none' })
    return
  }

  isLoadingRecords.value = true
  try {
    const recordIdsToDelete = records.value.map((r) => r._id)
    await Promise.all(recordIdsToDelete.map((id) => delChatRecordApi(id)))
    uni.showToast({ title: '已清空所有记录', icon: 'none' })
    records.value = []
    if (currentRecordId.value) {
      handleResetStory(false)
    }
  } catch (error) {
    console.error('Failed to clear records:', error)
    uni.showToast({ title: '清空失败', icon: 'none' })
  } finally {
    isLoadingRecords.value = false
  }
}

// 重置故事和评价
const handleResetStory = (showToast = true) => {
  // 停止任何正在播放的音频
  if (audioPlayerRef.value) {
    audioPlayerRef.value.pause()
  }

  // 清理故事原文
  story.value = {
    text: '',
    keywords: [],
  }

  // 清理用户复述和输入
  userRetelling.value = ''
  inputMessage.value = ''

  // 清理评价历史
  evaluationHistory.value.forEach((item) => {
    if (item.audioURL && item.audioURL.startsWith('blob:')) {
      URL.revokeObjectURL(item.audioURL)
    }
  })
  evaluationHistory.value = []
  currentEvaluationId.value = null
  currentRecordId.value = null

  // 清理音频相关状态
  if (currentAudioURL.value && currentAudioURL.value.startsWith('blob:')) {
    URL.revokeObjectURL(currentAudioURL.value)
  }
  currentAudioURL.value = ''
  currentAudioData.value = null

  // 重置状态标记
  isRecording.value = false
  isTranscribing.value = false
  isGenerating.value = false
  isEvaluating.value = false
  isStoryCollapsed.value = false
  isRetellingCollapsed.value = false
  isFeedbackCollapsed.value = false

  if (showToast) {
    uni.showToast({
      title: '已重置',
      icon: 'none',
      duration: 1500,
    })
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  // 清理 URL 对象
  if (currentAudioURL.value && currentAudioURL.value.startsWith('blob:')) {
    URL.revokeObjectURL(currentAudioURL.value)
  }

  // 清理评价历史中的音频 URL
  evaluationHistory.value.forEach((item) => {
    if (item.audioURL && item.audioURL.startsWith('blob:')) {
      URL.revokeObjectURL(item.audioURL)
    }
  })
})
</script>

<style scoped>
/* General Page Style */
.retell-story-page {
  background-color: var(--color-bg-gray, #f4f5f7);
  min-height: 100vh;
  font-family: var(--font-sans);
}

.content-container {
  padding: 16px;
  padding-bottom: 110px;
  /* 为底部输入框留出空间 */
}

/* Input Container (已移除固定定位，容器作为普通块级元素存在) */
.input-container {
  /* 可选：保留轻微的分隔样式，例如：border-top */
  /* border-top: 1px solid var(--color-border, #e9e9e9); */
}

/* Card Styles */
.card {
  background-color: var(--color-white, #fff);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid var(--color-border, #e9e9e9);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-border, #f0f0f0);
  position: relative;
  cursor: pointer;
}

.reset-btn {
  margin-left: auto;
  font-size: 16px;
  color: var(--color-text-secondary, #666);
  cursor: pointer;
  padding: 4px;
  transition: color 0.2s ease, transform 0.2s ease;
}

.reset-btn:hover {
  color: var(--color-primary, #007aff);
}

.reset-btn:active {
  transform: rotate(90deg);
  transition: transform 0.1s ease;
}

.card-icon {
  font-size: 20px;
  color: var(--color-primary, #007aff);
  margin-right: 12px;
  width: 24px;
  text-align: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary, #333);
}

.attempt-label {
  font-size: 14px;
  color: var(--color-text-secondary, #666);
  font-weight: normal;
  margin-left: 8px;
}

/* Story Card */
.story-content {
  transition: all 0.3s ease-in-out;
}

.story-text {
  font-size: 16px;
  line-height: 1.7;
  color: var(--color-text-main, #444);
  margin-bottom: 16px;
}

.story-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.button-group {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.generate-btn,
.select-note-btn {
  background-color: var(--color-primary, #007aff);
  color: var(--color-white, #fff);
  border: none;
  border-radius: 8px;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-note-btn {
  background-color: var(--color-secondary, #34c759);
  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.2);
}

.generate-btn:hover,
.select-note-btn:hover {
  transform: translateY(-2rpx);
}

.generate-btn:hover {
  background-color: var(--color-primary-dark, #0062cc);
  box-shadow: 0 8rpx 16rpx rgba(0, 122, 255, 0.3);
}

.select-note-btn:hover {
  background-color: #30b454;
  box-shadow: 0 8rpx 16rpx rgba(52, 199, 89, 0.3);
}

.generate-btn:active,
.select-note-btn:active {
  transform: translateY(2rpx);
}

.keywords-section {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
}

.keyword-tag {
  background-color: var(--color-primary, #007aff);
  color: var(--color-white, #fff);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
}

/* Retelling Card */
.retelling-content {
  transition: all 0.3s ease-in-out;
  padding-top: 16px;
}

.retelling-text {
  font-size: 16px;
  line-height: 1.7;
  color: var(--color-text-main, #444);
  min-height: 60px;
  font-style: italic;
  padding-top: 12px;
}

/* 替换为 z-audio-player 的样式 */
.retell-audio-player {
  margin-bottom: 16px;
}

/* Feedback Card */
.feedback-content {
  transition: all 0.3s ease-in-out;
}

.feedback-card .feedback-content {
  padding-top: 8px;
}

.feedback-summary {
  font-size: 16px;
  line-height: 1.6;
  color: var(--color-text-primary, #333);
  margin-bottom: 24px;
  padding: 12px;
  background-color: var(--color-bg-light, #f9fafb);
  border-radius: 8px;
}

.feedback-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-item {
  position: relative;
  padding-left: 36px;
}

.detail-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.metric-icon {
  position: absolute;
  left: 0;
  top: 2px;
  font-size: 18px;
  color: var(--color-primary-light-2, #5c94ff);
  width: 24px;
  text-align: center;
}

.metric-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary, #333);
}

.metric-feedback {
  font-size: 14px;
  color: var(--color-text-secondary, #666);
  line-height: 1.6;
}

/* History Card */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.history-item {
  background-color: var(--color-bg-light, #f9fafb);
  padding: 12px 16px;
  border-radius: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid var(--color-border, #eee);
  transition: all 0.2s ease;
  cursor: pointer;
}

.history-item.active {
  background-color: var(--color-primary-light-hover, #e8f4ff);
  border-color: var(--color-primary, #b3d8ff);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
}

.history-item:hover:not(.active) {
  background-color: var(--color-bg-hover, #f0f1f2);
  border-color: var(--color-border-hover, #ddd);
}

.history-item-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-item-info .attempt-number {
  font-weight: 600;
  color: var(--color-text-primary, #333);
  font-size: 15px;
}

.history-item-info .timestamp {
  color: var(--color-text-secondary, #888);
  font-size: 13px;
}

.history-item-icon {
  color: var(--color-icon-secondary, #bbb);
  font-size: 14px;
  transition: color 0.2s ease;
}

.history-item.active .history-item-icon,
.history-item:hover .history-item-icon {
  color: var(--color-primary, #007aff);
}

/* History Popup */
.history-popup-container {
  padding: 16px;
  padding-bottom: 32px; /* For safe area */
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary, #333);
}

.popup-close-icon {
  font-size: 20px;
  color: var(--color-text-secondary, #666);
  cursor: pointer;
}

.history-list-scroll {
  max-height: 50vh;
}

.card-header .history-btn {
  margin-left: auto;
  font-size: 14px;
  color: var(--color-text-secondary, #666);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
  border: 1px solid var(--color-border-light, #e0e0e0);
}

.card-header .history-btn:hover {
  background-color: var(--color-bg-hover, #f0f1f2);
}

/* Styles for the integrated Record List Popup */
.record-popup-content {
  background-color: white;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.record-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 1;
}
.record-popup-title {
  font-size: 16px;
  font-weight: bold;
}
.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}
.clear-all-btn {
  font-size: 14px;
  color: var(--color-danger, #f56c6c);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}
.clear-all-btn:hover {
  background-color: var(--color-danger-light-2, #fde2e2);
}
.record-popup-close-icon {
  cursor: pointer;
}
.record-popup-scroll-view {
  height: 50vh;
}
.record-popup-empty-state {
  text-align: center;
  padding: 40px;
  color: #999;
}
.record-popup-list {
  padding: 0 15px;
}
.record-popup-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 5px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}
.record-popup-item.active {
  background-color: var(--color-primary-light-hover, #e8f4ff);
}
.record-popup-item:hover {
  background-color: var(--color-bg-hover, #f5f5f5);
}
.record-popup-item:last-child {
  border-bottom: none;
}
.record-popup-item-main {
  flex: 1;
  min-width: 0;
}
.record-popup-item-delete {
  padding: 8px;
  color: var(--color-text-secondary, #999);
  border-radius: 50%;
  transition: all 0.2s ease;
}
.record-popup-item-delete:hover {
  background-color: var(--color-danger-light-2, #fde2e2);
  color: var(--color-danger, #f56c6c);
}
.record-popup-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.record-popup-date {
  font-size: 12px;
  color: #999;
}
.record-popup-keywords {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}
.record-popup-keyword-tag {
  margin-right: 5px;
}
.record-popup-summary {
  font-size: 14px;
  color: #333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
