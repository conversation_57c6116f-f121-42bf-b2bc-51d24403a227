<template>
  <view class="page-container">
    <view class="status_bar"></view>
    <view class="header">
      <view class="back-btn" @click="goBack">
        <i class="fas fa-arrow-left"></i>
      </view>
      <view class="title">lifeOS key</view>
    </view>
    
    <view class="content">
      <view class="key-container">
        <view class="key-icon">
          <i class="fas fa-key"></i>
        </view>
        <view class="key-title">输入 lifeOS key</view>
        <view class="key-description">
          请输入您的 lifeOS key 以启用相关功能
        </view>
        
        <view class="input-container">
          <u-input 
            v-model="tokenInput" 
            placeholder="请输入lifeOS key"
            :border="true"
            :clearable="true"
            height="100"
          />
        </view>
        
        <view class="button-container">
          <u-button 
            type="primary" 
            :loading="loading"
            @click="onConfirm"
            :disabled="!tokenInput.trim()"
          >
            确认保存
          </u-button>
        </view>
        
        <view v-if="currentKey" class="current-key">
          <view class="current-key-label">当前已保存的 key：</view>
          <view class="current-key-value">{{ maskedKey }}</view>
          <view class="clear-key" @click="clearKey">
            <i class="fas fa-trash"></i>
            清除
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { router } from '@/utils/tools'

const tokenInput = ref('')
const loading = ref(false)
const currentKey = ref('')

// 获取已保存的key
onMounted(() => {
  currentKey.value = uni.getStorageSync('lifeOSKey') || ''
})

// 遮蔽显示key
const maskedKey = computed(() => {
  if (!currentKey.value) return ''
  const key = currentKey.value
  if (key.length <= 8) return key
  return key.substring(0, 4) + '****' + key.substring(key.length - 4)
})

const onConfirm = async () => {
  if (!tokenInput.value.trim()) {
    uni.showToast({
      title: '请输入有效的key',
      icon: 'none'
    })
    return
  }
  
  loading.value = true
  try {
    uni.setStorageSync('lifeOSKey', tokenInput.value.trim())
    currentKey.value = tokenInput.value.trim()
    tokenInput.value = ''
    
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
    
    // 延迟返回上一页
    setTimeout(() => {
      goBack()
    }, 1500)
  } catch (error) {
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const clearKey = () => {
  uni.showModal({
    title: '确认清除',
    content: '确定要清除已保存的 lifeOS key 吗？',
    success: (res) => {
      if (res.confirm) {
        uni.removeStorageSync('lifeOSKey')
        currentKey.value = ''
        uni.showToast({
          title: '已清除',
          icon: 'success'
        })
      }
    }
  })
}

const goBack = () => {
  router.back()
}
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  display: flex;
  align-items: center;
  padding: 20rpx 40rpx;
  position: relative;
  
  .back-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 32rpx;
  }
  
  .title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 36rpx;
    font-weight: 600;
  }
}

.content {
  padding: 80rpx 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 200rpx);
}

.key-container {
  width: 100%;
  max-width: 600rpx;
  background: white;
  border-radius: 40rpx;
  padding: 80rpx 60rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.key-icon {
  margin-bottom: 40rpx;
  
  i {
    font-size: 120rpx;
    color: #f1c40f;
  }
}

.key-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.key-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
  line-height: 1.5;
}

.input-container {
  margin-bottom: 60rpx;
}

.button-container {
  margin-bottom: 40rpx;
}

.current-key {
  padding-top: 40rpx;
  border-top: 1rpx solid #eee;
  
  .current-key-label {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 20rpx;
  }
  
  .current-key-value {
    font-size: 28rpx;
    color: #333;
    background: #f5f5f5;
    padding: 20rpx;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    font-family: monospace;
  }
  
  .clear-key {
    font-size: 24rpx;
    color: #e74c3c;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10rpx;
    
    i {
      font-size: 20rpx;
    }
  }
}
</style>
