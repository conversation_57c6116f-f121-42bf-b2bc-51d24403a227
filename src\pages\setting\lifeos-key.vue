<template>
  <view class="page-container">
    <view class="status_bar"></view>
    <view class="header">
      <view class="back-btn" @click="goBack">
        <i class="fas fa-arrow-left"></i>
      </view>
      <view class="title">lifeOS key</view>
    </view>
    
    <view class="content">
      <view class="key-container">
        <view class="key-icon">
          <i class="fas fa-key"></i>
        </view>
        <view class="key-title">输入 lifeOS key</view>
        <view class="key-description">
          请输入您的 lifeOS key 以启用相关功能
        </view>
        
        <view class="input-container">
          <u-input 
            v-model="tokenInput" 
            placeholder="请输入lifeOS key"
            :border="true"
            :clearable="true"
            height="100"
          />
        </view>
        
        <view class="button-container">
          <u-button 
            type="primary" 
            :loading="loading"
            @click="onConfirm"
            :disabled="!tokenInput.trim()"
          >
            确认保存
          </u-button>
        </view>
        

      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { router } from '@/utils/tools'

const tokenInput = ref('')
const loading = ref(false)

const onConfirm = async () => {
  if (!tokenInput.value.trim()) {
    uni.showToast({
      title: '请输入有效的key',
      icon: 'none'
    })
    return
  }

  loading.value = true
  try {
    // 使用 uniCloud.importObject() 调用后端验证接口
    const lifeOSObj = uniCloud.importObject('lifeOS')
    const validateResult = await lifeOSObj.validateKey({
      key: tokenInput.value.trim()
    })

    if (validateResult === 'xpzgg') {
      // 验证成功，保存key
      uni.setStorageSync('lifeOSKey', tokenInput.value.trim())
      tokenInput.value = ''

      uni.showToast({
        title: '验证成功',
        icon: 'success'
      })

      // 延迟返回上一页
      setTimeout(() => {
        goBack()
      }, 1500)
    } else {
      // 验证失败
      uni.showToast({
        title: 'key验证失败，请检查后重试',
        icon: 'none',
        duration: 2000
      })
    }
  } catch (error) {
    console.error('验证key时发生错误：', error)
    uni.showToast({
      title: '网络错误，请检查网络连接后重试',
      icon: 'none',
      duration: 2000
    })
  } finally {
    loading.value = false
  }
}



const goBack = () => {
  router.back()
}
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  display: flex;
  align-items: center;
  padding: 20rpx 40rpx;
  position: relative;
  
  .back-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 32rpx;
  }
  
  .title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 36rpx;
    font-weight: 600;
  }
}

.content {
  padding: 80rpx 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 200rpx);
}

.key-container {
  width: 100%;
  max-width: 600rpx;
  background: white;
  border-radius: 40rpx;
  padding: 80rpx 60rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.key-icon {
  margin-bottom: 40rpx;
  
  i {
    font-size: 120rpx;
    color: #f1c40f;
  }
}

.key-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.key-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
  line-height: 1.5;
}

.input-container {
  margin-bottom: 60rpx;
}

.button-container {
  margin-bottom: 40rpx;
}


</style>
